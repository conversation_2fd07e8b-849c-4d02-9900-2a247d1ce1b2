//
//  ProductPromotionDetailViewController.swift
//  Shuxiaoqi
//
//  Created by Cascade on 2025/08/23.
//
//  带货商品详情页（iOS14+，UIKit+SnapKit）
//

import UIKit
import SnapKit

/// 带货商品详情页
class ProductPromotionDetailViewController: BaseViewController {
    
    // MARK: - UI
    private let scrollView = UIScrollView()
    private let containerView = UIView()
    
    // 顶部商品卡片
    private let productCard = UIView()
    private let productImageView = UIImageView()
    private let productTitleLabel = UILabel()
    private let productPriceLabel = UILabel()
    private let productSubInfoLabel = UILabel()
    private let salesBadgeContainer = UIView()
    private let salesBadgeLabel = UILabel()
    
    // 分组标题
    private func sectionHeaderLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.textColor = UIColor.appTitleText
        label.font = UIFont.boldSystemFont(ofSize: 16)
        return label
    }
    
    private let settingsHeader = UILabel()
    private let statsHeader = UILabel()
    private let videoHeader = UILabel()
    
    // 设置区
    private let positionRow = UIView()
    private let positionTitle = UILabel()
    private let positionButton = UIButton(type: .system)
    
    private let weightRow = UIView()
    private let weightTitle = UILabel()
    private let weightField = UITextField()
    private let positionValueLabel = UILabel()
    private let positionDivider = UIView()
    private let weightDivider = UIView()
    
    private let saveButton = UIButton(type: .system)
    
    // 统计卡片
    private let statsCard = UIView()
    private let statAmount = StatItemView(title: "总销售额", value: "¥ 128,560")
    private let statToday = StatItemView(title: "今日销量", value: "256")
    private let statHistory = StatItemView(title: "历史销量", value: "2,856")
    
    // 挂载视频
    private let addVideoButton = UIButton(type: .system)
    private var videoCollection: UICollectionView!
    
    // 导航右侧菜单
    private lazy var moreButton: UIButton = {
        let b = UIButton(type: .system)
        if let img = UIImage(systemName: "ellipsis") {
            b.setImage(img, for: .normal)
        } else {
            b.setTitle("…", for: .normal)
        }
        b.tintColor = UIColor(hex: "#666666")
        b.addTarget(self, action: #selector(onMore), for: .touchUpInside)
        return b
    }()
    
    // 示例视频数据
    private let videos: [VideoModel] = [
        .init(title: "面霜使用技巧分享", price: "¥ 3,560", convert: "转化率 20%", play: "12.5万", duration: "3:20"),
        .init(title: "面霜开箱测评", price: "¥ 12,880", convert: "转化率 50%", play: "42.5万", duration: "20:00")
    ]
    
    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "带货详情"
        contentView.backgroundColor = .appBackgroundGray
        navRightItems = [moreButton]
        setupProductCard()
        setupSettings()
        setupStatsCard()
        setupVideos()
        buildLayout()
        fillMockData()
    }
    
    // MARK: - UI Build
    private func buildLayout() {
        // Scroll
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        scrollView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
        
        // 分组标题文字
        settingsHeader.text = "带货设置"
        settingsHeader.textColor = UIColor.appTitleText
        settingsHeader.font = UIFont.boldSystemFont(ofSize: 16)
        
        statsHeader.text = "销售统计"
        statsHeader.textColor = UIColor.appTitleText
        statsHeader.font = UIFont.boldSystemFont(ofSize: 16)
        
        videoHeader.text = "挂载视频"
        videoHeader.textColor = UIColor.appTitleText
        videoHeader.font = UIFont.boldSystemFont(ofSize: 16)
        
        // 放入容器
        [productCard,
         settingsHeader, positionRow, weightRow, saveButton,
         statsHeader, statsCard,
         videoHeader, addVideoButton,
        ].forEach { containerView.addSubview($0) }
        containerView.addSubview(videoCollection)
        
        productCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(88)
        }
        
        settingsHeader.snp.makeConstraints { make in
            make.top.equalTo(productCard.snp.bottom).offset(16)
            make.left.equalTo(productCard)
        }
        
        positionRow.snp.makeConstraints { make in
            make.top.equalTo(settingsHeader.snp.bottom).offset(12)
            make.left.right.equalTo(productCard)
            make.height.equalTo(75)
        }
        
        weightRow.snp.makeConstraints { make in
            make.top.equalTo(positionRow.snp.bottom).offset(8)
            make.left.right.equalTo(productCard)
            make.height.equalTo(75)
        }
        
        saveButton.snp.makeConstraints { make in
            make.top.equalTo(weightRow.snp.bottom).offset(16)
            make.left.right.equalTo(productCard)
            make.height.equalTo(44)
        }
        
        statsHeader.snp.makeConstraints { make in
            make.top.equalTo(saveButton.snp.bottom).offset(24)
            make.left.equalTo(productCard)
        }
        
        statsCard.snp.makeConstraints { make in
            make.top.equalTo(statsHeader.snp.bottom).offset(12)
            make.left.right.equalTo(productCard)
        }
        
        videoHeader.snp.makeConstraints { make in
            make.top.equalTo(statsCard.snp.bottom).offset(24)
            make.left.equalTo(productCard)
        }
        
        addVideoButton.snp.makeConstraints { make in
            make.centerY.equalTo(videoHeader)
            make.right.equalTo(productCard)
            make.width.height.equalTo(24)
        }
        
        videoCollection.snp.makeConstraints { make in
            make.top.equalTo(videoHeader.snp.bottom).offset(12)
            make.left.right.equalTo(productCard)
            make.height.equalTo(180)
            make.bottom.equalToSuperview().offset(-24)
        }
    }
    
    // 顶部商品卡片
    private func setupProductCard() {
        productCard.backgroundColor = .white
        productCard.layer.cornerRadius = 10
        productCard.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        productCard.layer.shadowOpacity = 1
        productCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        productCard.layer.shadowRadius = 6
        
        productImageView.contentMode = .scaleAspectFill
        productImageView.clipsToBounds = true
        productImageView.layer.cornerRadius = 8
        if let photo = UIImage(systemName: "photo") {
            productImageView.image = photo
            productImageView.tintColor = UIColor(hex: "#BBBBBB")
            productImageView.backgroundColor = UIColor(hex: "#F3F3F3")
            productImageView.contentMode = .scaleAspectFit
        }
        
        productTitleLabel.textColor = UIColor.appTitleText
        productTitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        productTitleLabel.numberOfLines = 2
        
        productPriceLabel.textColor = UIColor.appThemeOrange
        productPriceLabel.font = UIFont.boldSystemFont(ofSize: 18)
        productPriceLabel.textAlignment = .right
        
        productSubInfoLabel.textColor = UIColor(hex: "#999999")
        productSubInfoLabel.font = UIFont.systemFont(ofSize: 12)
        productSubInfoLabel.numberOfLines = 2
        
        [productImageView, productTitleLabel, productPriceLabel, salesBadgeContainer, productSubInfoLabel].forEach { productCard.addSubview($0) }

        // 销量徽标样式
        salesBadgeContainer.backgroundColor = UIColor(hex: "#FAEDE3")
        salesBadgeContainer.layer.cornerRadius = 3
        salesBadgeContainer.clipsToBounds = true
        salesBadgeLabel.textColor = UIColor(hex: "#C4844F")
        salesBadgeLabel.font = UIFont.systemFont(ofSize: 10)
        salesBadgeContainer.addSubview(salesBadgeLabel)
        
        productImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(10)
            make.bottom.equalToSuperview().offset(-12)
            make.width.height.equalTo(64)
        }
        
        productPriceLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView)
            make.right.equalToSuperview().offset(-12)
        }
        
        productTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView)
            make.left.equalTo(productImageView.snp.right).offset(12)
            make.right.lessThanOrEqualTo(productPriceLabel.snp.left).offset(-8)
        }
        // 销量徽标位置：距离标题 6pt，高度 15pt
        salesBadgeContainer.snp.makeConstraints { make in
            make.left.equalTo(productTitleLabel)
            make.top.equalTo(productTitleLabel.snp.bottom).offset(6)
            make.height.equalTo(15)
        }
        // 徽标内部文字左右内边距 6pt
        salesBadgeLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(6)
            make.right.equalToSuperview().offset(-6)
            make.centerY.equalToSuperview()
        }
        // 让容器宽度由文字内容驱动（右侧跟随文字右边+6）
        salesBadgeContainer.snp.makeConstraints { make in
            make.right.equalTo(salesBadgeLabel.snp.right).offset(6)
        }
        
        productSubInfoLabel.snp.makeConstraints { make in
            make.left.equalTo(productTitleLabel)
            make.right.equalToSuperview().offset(-12)
            make.top.equalTo(salesBadgeContainer.snp.bottom).offset(6)
            make.bottom.lessThanOrEqualToSuperview().offset(-4)
        }
    }
    
    // 设置区
    private func setupSettings() {
        // 商品位置
        positionTitle.text = "商品位置"
        positionTitle.textColor = UIColor.appBodyText
        positionTitle.font = UIFont.systemFont(ofSize: 14)
        
        positionValueLabel.text = "精选商品"
        positionValueLabel.textColor = UIColor.appTitleText
        positionValueLabel.font = UIFont.systemFont(ofSize: 14)
        
        positionButton.setTitle(nil, for: .normal)
        if let down = UIImage(systemName: "chevron.down") {
            positionButton.setImage(down, for: .normal)
            positionButton.tintColor = UIColor(hex: "#999999")
        }
        positionButton.addTarget(self, action: #selector(onSelectPosition), for: .touchUpInside)
        
        positionDivider.backgroundColor = UIColor(hex: "#EFEFEF")
        
        positionRow.backgroundColor = .white
        positionRow.addSubview(positionTitle)
        positionRow.addSubview(positionValueLabel)
        positionRow.addSubview(positionButton)
        positionRow.addSubview(positionDivider)
        
        // 标题在上，值在下
        positionTitle.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
        }
        positionValueLabel.snp.makeConstraints { make in
            make.top.equalTo(positionTitle.snp.bottom).offset(12)
            make.left.equalTo(positionTitle)
            make.right.lessThanOrEqualTo(positionButton.snp.left).offset(-8)
        }
        // 右侧下拉按钮
        positionButton.snp.makeConstraints { make in
            make.centerY.equalTo(positionValueLabel)
            make.right.equalToSuperview().offset(-12)
            make.width.height.equalTo(24)
        }
        // 底部分割线
        positionDivider.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        // 排序权重
        weightTitle.text = "排序权重"
        weightTitle.textColor = UIColor.appBodyText
        weightTitle.font = UIFont.systemFont(ofSize: 14)
        
        weightField.text = "100"
        weightField.textAlignment = .left
        weightField.keyboardType = .numberPad
        weightField.font = UIFont.systemFont(ofSize: 14)
        
        weightDivider.backgroundColor = UIColor(hex: "#EFEFEF")
        
        weightRow.backgroundColor = .white
        weightRow.addSubview(weightTitle)
        weightRow.addSubview(weightField)
        weightRow.addSubview(weightDivider)
        
        // 标题在上，输入在下
        weightTitle.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
        }
        weightField.snp.makeConstraints { make in
            make.top.equalTo(weightTitle.snp.bottom).offset(12)
            make.left.equalTo(weightTitle)
            make.right.lessThanOrEqualToSuperview().offset(-16)
            make.height.greaterThanOrEqualTo(22)
        }
        // 底部分割线
        weightDivider.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        // 保存按钮
        saveButton.setTitle("保存设置", for: .normal)
        saveButton.backgroundColor = .appThemeOrange
        saveButton.setTitleColor(.white, for: .normal)
        saveButton.layer.cornerRadius = 8
        saveButton.addTarget(self, action: #selector(onSave), for: .touchUpInside)
    }
    
    // 统计卡片
    private func setupStatsCard() {
        statsCard.backgroundColor = .white
        statsCard.layer.cornerRadius = 12
        statsCard.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        statsCard.layer.shadowOpacity = 1
        statsCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        statsCard.layer.shadowRadius = 6
        
        [statAmount, statToday, statHistory].forEach { statsCard.addSubview($0) }
        
        // 三等分布局
        statAmount.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview().inset(16)
            make.width.equalTo(statsCard).multipliedBy(1.0/3.0).offset(-16/3.0)
        }
        statToday.snp.makeConstraints { make in
            make.top.bottom.equalTo(statAmount)
            make.left.equalTo(statAmount.snp.right)
            make.width.equalTo(statAmount)
        }
        statHistory.snp.makeConstraints { make in
            make.top.bottom.equalTo(statAmount)
            make.left.equalTo(statToday.snp.right)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(statAmount)
        }
        
        // 竖向分隔线
        let v1 = UIView(); v1.backgroundColor = UIColor(hex: "#EFEFEF")
        let v2 = UIView(); v2.backgroundColor = UIColor(hex: "#EFEFEF")
        statsCard.addSubview(v1); statsCard.addSubview(v2)
        v1.snp.makeConstraints { make in
            make.centerY.equalTo(statsCard)
            make.left.equalTo(statAmount.snp.right)
            make.width.equalTo(0.5)
            make.height.equalTo(36)
        }
        v2.snp.makeConstraints { make in
            make.centerY.equalTo(statsCard)
            make.left.equalTo(statToday.snp.right)
            make.width.equalTo(0.5)
            make.height.equalTo(36)
        }
    }
    
    // 视频区
    private func setupVideos() {
        // header 右侧加号
        if let plus = UIImage(systemName: "plus.circle") {
            addVideoButton.setImage(plus, for: .normal)
            addVideoButton.tintColor = UIColor(hex: "#333333")
        } else {
            addVideoButton.setTitle("+", for: .normal)
        }
        addVideoButton.addTarget(self, action: #selector(onAddVideo), for: .touchUpInside)
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        let itemW: CGFloat = 160
        let itemH: CGFloat = 180
        layout.itemSize = CGSize(width: itemW, height: itemH)
        
        videoCollection = UICollectionView(frame: .zero, collectionViewLayout: layout)
        videoCollection.backgroundColor = .clear
        videoCollection.showsHorizontalScrollIndicator = false
        videoCollection.delegate = self
        videoCollection.dataSource = self
        videoCollection.register(PromotionDetailVideoCell.self, forCellWithReuseIdentifier: PromotionDetailVideoCell.reuseId)
    }
    
    private func fillMockData() {
        productTitleLabel.text = "高端护肤精华面霜"
        productPriceLabel.text = "¥ 368"
        salesBadgeLabel.text = "销量 2,856件"
        productSubInfoLabel.text = "佣金比例 20% · 预计佣金 ¥79.80"
    }
    
    // MARK: - Actions
    @objc private func onMore() {
        let sheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        sheet.addAction(UIAlertAction(title: "编辑商品", style: .default))
        sheet.addAction(UIAlertAction(title: "下架", style: .destructive))
        sheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(sheet, animated: true)
    }
    
    @objc private func onSelectPosition() {
        let sheet = UIAlertController(title: "选择商品位置", message: nil, preferredStyle: .actionSheet)
        ["精选商品", "橱窗商品", "普通商品"].forEach { title in
            sheet.addAction(UIAlertAction(title: title, style: .default, handler: { [weak self] _ in
                self?.positionValueLabel.text = title
            }))
        }
        sheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(sheet, animated: true)
    }
    
    @objc private func onSave() {
        view.endEditing(true)
        let alert = UIAlertController(title: "已保存", message: "带货设置已更新", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func onAddVideo() {
        let alert = UIAlertController(title: "添加视频", message: "此处接入挂载视频选择逻辑", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - CollectionView
extension ProductPromotionDetailViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return videos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PromotionDetailVideoCell.reuseId, for: indexPath) as! PromotionDetailVideoCell
        cell.configure(videos[indexPath.item])
        return cell
    }
}

// MARK: - Subviews
/// 统计项
private class StatItemView: UIView {
    private let valueLabel = UILabel()
    private let titleLabel = UILabel()
    
    init(title: String, value: String) {
        super.init(frame: .zero)
        valueLabel.text = value
        valueLabel.textColor = UIColor.appTitleText
        valueLabel.font = UIFont.boldSystemFont(ofSize: 18)
        valueLabel.textAlignment = .center
        
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#999999")
        titleLabel.font = UIFont.systemFont(ofSize: 12)
        titleLabel.textAlignment = .center
        
        addSubview(valueLabel)
        addSubview(titleLabel)
        
        valueLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(6)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
}

/// 视频模型
private struct VideoModel {
    let title: String
    let price: String
    let convert: String
    let play: String
    let duration: String
}

/// 视频卡片（详情页自用，避免与全局 VideoItemCell 冲突）
private class PromotionDetailVideoCell: UICollectionViewCell {
    static let reuseId = "PromotionDetailVideoCell"
    
    private let thumb = UIImageView()
    private let playBadge = UILabel()
    private let durationBadge = UILabel()
    private let titleLabel = UILabel()
    private let bottomRow = UIStackView()
    private let priceLabel = UILabel()
    private let convertLabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = .clear
        let container = UIView()
        container.backgroundColor = .white
        container.layer.cornerRadius = 10
        container.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        container.layer.shadowOpacity = 1
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 6
        contentView.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // thumbnail
        thumb.backgroundColor = UIColor(hex: "#F2F2F2")
        if let img = UIImage(systemName: "photo") { thumb.image = img; thumb.tintColor = UIColor(hex: "#BBBBBB"); thumb.contentMode = .scaleAspectFit }
        thumb.clipsToBounds = true
        thumb.layer.cornerRadius = 8
        container.addSubview(thumb)
        thumb.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview().inset(8)
            make.height.equalTo(100)
        }
        
        // overlay badges
        playBadge.textColor = .white
        playBadge.font = UIFont.systemFont(ofSize: 10)
        playBadge.text = ""
        playBadge.backgroundColor = UIColor.black.withAlphaComponent(0.35)
        playBadge.layer.cornerRadius = 4
        playBadge.clipsToBounds = true
        playBadge.textAlignment = .center
        container.addSubview(playBadge)
        playBadge.snp.makeConstraints { make in
            make.left.equalTo(thumb).offset(6)
            make.bottom.equalTo(thumb).offset(-6)
            make.height.equalTo(18)
        }
        
        durationBadge.textColor = .white
        durationBadge.font = UIFont.systemFont(ofSize: 10)
        durationBadge.text = ""
        durationBadge.backgroundColor = UIColor.black.withAlphaComponent(0.35)
        durationBadge.layer.cornerRadius = 4
        durationBadge.clipsToBounds = true
        durationBadge.textAlignment = .center
        container.addSubview(durationBadge)
        durationBadge.snp.makeConstraints { make in
            make.right.equalTo(thumb).offset(-6)
            make.bottom.equalTo(thumb).offset(-6)
            make.height.equalTo(18)
        }
        
        titleLabel.textColor = UIColor.appTitleText
        titleLabel.font = UIFont.systemFont(ofSize: 14)
        titleLabel.numberOfLines = 2
        container.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(thumb.snp.bottom).offset(6)
            make.left.right.equalToSuperview().inset(10)
        }
        
        // bottom row
        bottomRow.axis = .horizontal
        bottomRow.alignment = .fill
        bottomRow.distribution = .fill
        bottomRow.spacing = 8
        
        priceLabel.textColor = UIColor.appTitleText
        priceLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        
        convertLabel.textColor = UIColor(hex: "#999999")
        convertLabel.font = UIFont.systemFont(ofSize: 12)
        convertLabel.textAlignment = .right
        
        bottomRow.addArrangedSubview(priceLabel)
        bottomRow.addArrangedSubview(UIView())
        bottomRow.addArrangedSubview(convertLabel)
        container.addSubview(bottomRow)
        bottomRow.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.left.right.equalToSuperview().inset(10)
            make.bottom.equalToSuperview().offset(-10)
        }
    }
    
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    func configure(_ m: VideoModel) {
        titleLabel.text = m.title
        priceLabel.text = m.price
        convertLabel.text = m.convert
        playBadge.text = "  \u{25B6}\u{FE0E}  " + m.play // ▶︎
        durationBadge.text = "  " + m.duration + "  "
        // intrinsic sizes
        playBadge.sizeToFit(); durationBadge.sizeToFit()
    }
}
